<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Management</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- <script src="/SOLID-Principles/views/users/assets/js/users.js"></script> -->
</head>
<body>
    <div class="container mt-5">
        <h1 class="mb-4">User Management</h1>

        <?php if (isset($_GET['success'])): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                User created successfully!
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if (isset($_GET['error'])): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                Error: <?php echo htmlspecialchars($_GET['error']); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <div id="alertMessage" class="alert" style="display: none;"></div>

        <div class="card mb-4">
            <div class="card-header">
                <h5 id="formTitle">Create User</h5>
            </div>
            <div class="card-body">
            <form id="userForm" action="/SOLID-Principles/api/user.php" method="POST">
                <input type="hidden" id="userId" name="id" value="">
                <input type="hidden" name="_html_form" value="1">
                
                <div class="mb-3">
                    <label for="name" class="form-label">Name</label>
                    <input type="text" class="form-control" id="name" name="name" required>
                </div>
                
                <div class="mb-3">
                    <label for="email" class="form-label">Email</label>
                    <input type="email" class="form-control" id="email" name="email" required>
                </div>
                
                <button type="submit" class="btn btn-primary">Save</button>
                <button type="reset" class="btn btn-secondary">Cancel</button>
            </form>
                        </div>
        </div>
        

        <div class="card">
            <div class="card-header">
                <h5>Users</h5>
            </div>
            <div class="card-body">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Name</th>
                            <th>Email</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody id="userTableBody">
                        <?php
                       
                        try {
                            require_once __DIR__ . '/../../db/MYSQLConnection.php';
                            require_once __DIR__ . '/../../repositories/UserRepository.php';

                            $db = new MYSQLConnection();
                            $userRepository = new UserRepository($db);
                            $users = $userRepository->getAll();

                            foreach ($users as $user) {
                                echo "<tr>";
                                echo "<td>" . htmlspecialchars($user->id) . "</td>";
                                echo "<td>" . htmlspecialchars($user->name) . "</td>";
                                echo "<td>" . htmlspecialchars($user->email) . "</td>";
                                echo "<td>";
                                echo "<a href='#' class='btn btn-sm btn-warning me-1'>Edit</a>";
                                echo "<a href='/SOLID-Principles/api/user.php?id=" . $user->id . "' class='btn btn-sm btn-danger delete-user'>Delete</a>";
                                echo "</td>";
                                echo "</tr>";
                            }
                        } catch (Exception $e) {
                            echo "<tr><td colspan='4'>Error loading users: " . htmlspecialchars($e->getMessage()) . "</td></tr>";
                        }
                        ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>