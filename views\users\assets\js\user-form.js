// User form management - follows SRP
class UserFormManager {
    constructor() {
        this.form = document.getElementById('userForm');
        this.userIdField = document.getElementById('userId');
        this.nameField = document.getElementById('name');
        this.emailField = document.getElementById('email');
        this.formTitle = document.getElementById('formTitle');
        this.methodField = document.getElementById('formMethod');
        
        this.initializeEventListeners();
    }
    
    initializeEventListeners() {
        const resetButton = document.querySelector('button[type="reset"]');
        if (resetButton) {
            resetButton.addEventListener('click', (e) => {
                e.preventDefault();
                this.resetForm();
            });
        }
    }
    
    editUser(id, name, email) {
        this.userIdField.value = id;
        this.nameField.value = name;
        this.emailField.value = email;
        this.formTitle.textContent = 'Edit User';
        this.methodField.value = 'PUT';
        this.form.action = '/SOLID-Principles/api/user.php?id=' + id;
        
        this.form.scrollIntoView({ behavior: 'smooth' });
    }
    
    resetForm() {
        this.userIdField.value = '';
        this.nameField.value = '';
        this.emailField.value = '';
        this.formTitle.textContent = 'Create User';
        this.methodField.value = 'POST';
        this.form.action = '/SOLID-Principles/api/user.php';
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    window.userFormManager = new UserFormManager();
});

// Global function for onclick handlers (backward compatibility)
function editUser(id, name, email) {
    if (window.userFormManager) {
        window.userFormManager.editUser(id, name, email);
    }
}
