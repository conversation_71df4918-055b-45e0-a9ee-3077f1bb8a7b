// User form management - follows SRP
class UserFormManager {
    constructor() {
        this.form = document.getElementById('userForm');
        this.userIdField = document.getElementById('userId');
        this.nameField = document.getElementById('name');
        this.emailField = document.getElementById('email');
        this.formTitle = document.getElementById('formTitle');
        this.methodField = document.getElementById('formMethod');
        
        this.initializeEventListeners();
    }
    
    initializeEventListeners() {
        const resetButton = document.querySelector('button[type="reset"]');
        if (resetButton) {
            resetButton.addEventListener('click', (e) => {
                e.preventDefault();
                this.resetForm();
            });
        }

        // Add delete confirmation
        const deleteButtons = document.querySelectorAll('.delete-user');
        deleteButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                if (!confirm('Are you sure you want to delete this user?')) {
                    e.preventDefault();
                }
            });
        });
    }
    
    editUser(id, name, email) {
        this.userIdField.value = id;
        this.nameField.value = name;
        this.emailField.value = email;
        this.formTitle.textContent = 'Edit User';
        this.methodField.value = 'PUT';
        this.form.action = '/SOLID-Principles/api/user.php?id=' + id;
        
        this.form.scrollIntoView({ behavior: 'smooth' });
    }
    
    resetForm() {
        this.userIdField.value = '';
        this.nameField.value = '';
        this.emailField.value = '';
        this.formTitle.textContent = 'Create User';
        this.methodField.value = 'POST';
        this.form.action = '/SOLID-Principles/api/user.php';
    }

    deleteUser(id) {
        if (!confirm('Are you sure you want to delete this user?')) {
            return;
        }

        fetch(`/SOLID-Principles/api/user.php?id=${id}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                // Reload the page to show updated list
                window.location.href = '/SOLID-Principles/views/users/index_solid.php?success=1&message=' + encodeURIComponent('User deleted successfully');
            } else {
                // Show error
                window.location.href = '/SOLID-Principles/views/users/index_solid.php?error=' + encodeURIComponent(data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            window.location.href = '/SOLID-Principles/views/users/index_solid.php?error=' + encodeURIComponent('Error deleting user');
        });
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    window.userFormManager = new UserFormManager();
});

// Global functions for onclick handlers (backward compatibility)
function editUser(id, name, email) {
    if (window.userFormManager) {
        window.userFormManager.editUser(id, name, email);
    }
}

function deleteUser(id) {
    if (window.userFormManager) {
        window.userFormManager.deleteUser(id);
    }
}
