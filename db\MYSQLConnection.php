<?php
require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/DatabaseConnectionInterface.php';

class MYSQLConnection implements DatabaseConnectionInterface{
    private $connection;
    public function __construct(){
        $this->connection = new mysqli(DB_HOST, DB_USER, DB_PASS, DB_NAME );
        if($this->connection->connect_error){
            die('Database Connection failed: '  . $this->connection->connect_error);
        }
    }

    public function getConnection(){
        return $this->connection;
    }
}