<?php
require_once __DIR__ . '/../entities/user.php';
require_once __DIR__ . '/../services/UserServiceInterface.php';
require_once __DIR__ . '/../exception/ValidationException.php';
require_once __DIR__ . '/../exception/DatabaseException.php';

class UserController {
    private $userService;

    public function __construct(UserServiceInterface $userService) {
        $this->userService = $userService;
    }

    public function index() {
        try {
            $users = $this->userService->getAll();
            return $this->jsonResponse(['status' => 'success', 'data' => $users]);
        } catch (Exception $e) {
            return $this->jsonResponse(['status' => 'error', 'message' => $e->getMessage()], 500);  
        }
    }

    public function show($id) {
        try {
            $user = $this->userService->getById($id);
            return $this->jsonResponse(['status' => 'success', 'data' => $user]);
        } catch (Exception $e) {
            return $this->jsonResponse(['status' => 'error', 'message' => $e->getMessage()], 500);
        }
    }

    public function create() {
        try {
        
            $rawInput = file_get_contents('php://input');
            if (!empty($rawInput)) {
                $data = json_decode($rawInput, true);
            } else {
             
                $data = $_POST;
            }
    
            if (empty($data)) {
                throw new ValidationException("Invalid input data");
            }
    
            $user = new User(null, $data['name'], $data['email']);
            $result = $this->userService->create($user);
            
            if (isset($data['_html_form'])) {
                
                header('Location: /SOLID-Principles/views/users/index.php?success=1');
                exit;
            }
            
            return json_encode(['status' => 'success', 'message' => 'User created successfully']);
        } catch (Exception $e) {
            if (isset($_POST['_html_form'])) {
               
                header('Location: /SOLID-Principles/views/users/index.php?error=' . urlencode($e->getMessage()));
                exit;
            }
            return json_encode(['status' => 'error', 'message' => $e->getMessage()]);
        }
    }
    public function update($id) {
        try {
            $data = json_decode(file_get_contents('php://input'), true);

            if (!$data) {
                throw new ValidationException("Invalid input data");
            }

            $user = new User($id, $data['name'], $data['email']);
            $result = $this->userService->update($user);
            return $this->jsonResponse(['status' => 'success', 'message' => 'User updated successfully']);
        } catch (Exception $e) {
            return $this->jsonResponse(['status' => 'error', 'message' => $e->getMessage()], 500);
        }
    }
    
    public function delete($id) {
        try {
            $result = $this->userService->delete($id);
            return $this->jsonResponse(['status' => 'success', 'message' => 'User deleted successfully']);
        } catch (Exception $e) {
            return $this->jsonResponse(['status' => 'error', 'message' => $e->getMessage()], 500);
        }
    }

    private function jsonResponse($data, $status = 200) {
        http_response_code($status);
        return json_encode($data);
    }
}