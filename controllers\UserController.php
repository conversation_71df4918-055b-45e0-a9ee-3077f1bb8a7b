<?php
require_once __DIR__ . '/../entities/user.php';
require_once __DIR__ . '/../services/UserServiceInterface.php';
require_once __DIR__ . '/../exception/ValidationException.php';
require_once __DIR__ . '/../exception/DatabaseException.php';

class UserController {
    private $userService;

    public function __construct(UserServiceInterface $userService) {
        $this->userService = $userService;
    }

    public function index() {
        try {
            $users = $this->userService->getAll();
            return $this->jsonResponse(['status' => 'success', 'data' => $users]);
        } catch (Exception $e) {
            return $this->jsonResponse(['status' => 'error', 'message' => $e->getMessage()], 500);  
        }
    }

    public function show($id) {
        try {
            $user = $this->userService->getById($id);
            return $this->jsonResponse(['status' => 'success', 'data' => $user]);
        } catch (Exception $e) {
            return $this->jsonResponse(['status' => 'error', 'message' => $e->getMessage()], 500);
        }
    }

    public function create() {
        try {
            // Debug logging
            error_log("CREATE REQUEST DEBUG:");
            error_log("Content-Type: " . ($_SERVER['CONTENT_TYPE'] ?? 'not set'));
            error_log("Request Method: " . $_SERVER['REQUEST_METHOD']);

            $rawInput = file_get_contents('php://input');
            error_log("Raw input: " . $rawInput);
            error_log("POST data: " . print_r($_POST, true));

            if (!empty($rawInput)) {
                $data = json_decode($rawInput, true);
                error_log("Decoded JSON: " . print_r($data, true));
                error_log("JSON decode error: " . json_last_error_msg());
            } else {
                $data = $_POST;
            }

            if (empty($data)) {
                error_log("Data is empty - throwing ValidationException");
                throw new ValidationException("Invalid input data - no data received");
            }
    
            $user = new User(null, $data['name'], $data['email']);
            $result = $this->userService->create($user);
            
            if (isset($data['_html_form'])) {
                
                header('Location: /SOLID-Principles/views/users/index.php?success=1');
                exit;
            }
            
            return json_encode(['status' => 'success', 'message' => 'User created successfully']);
        } catch (Exception $e) {
            if (isset($_POST['_html_form'])) {
               
                header('Location: /SOLID-Principles/views/users/index.php?error=' . urlencode($e->getMessage()));
                exit;
            }
            return json_encode(['status' => 'error', 'message' => $e->getMessage()]);
        }
    }
    public function update($id) {
        try {
            // Debug logging
            error_log("UPDATE REQUEST DEBUG:");
            error_log("Content-Type: " . ($_SERVER['CONTENT_TYPE'] ?? 'not set'));
            error_log("Request Method: " . $_SERVER['REQUEST_METHOD']);
            error_log("ID parameter: " . $id);

            $rawInput = file_get_contents('php://input');
            error_log("Raw input: " . $rawInput);

            $data = json_decode($rawInput, true);
            error_log("Decoded JSON: " . print_r($data, true));
            error_log("JSON decode error: " . json_last_error_msg());

            if (!$data) {
                error_log("Data is null/false - throwing ValidationException");
                throw new ValidationException("Invalid input data - JSON decode failed or empty");
            }

            $user = new User($id, $data['name'], $data['email']);
            $result = $this->userService->update($user);
            return $this->jsonResponse(['status' => 'success', 'message' => 'User updated successfully']);
        } catch (Exception $e) {
            return $this->jsonResponse(['status' => 'error', 'message' => $e->getMessage()], 500);
        }
    }
    
    public function delete($id) {
        try {
            $result = $this->userService->delete($id);
            return $this->jsonResponse(['status' => 'success', 'message' => 'User deleted successfully']);
        } catch (Exception $e) {
            return $this->jsonResponse(['status' => 'error', 'message' => $e->getMessage()], 500);
        }
    }

    private function jsonResponse($data, $status = 200) {
        http_response_code($status);
        return json_encode($data);
    }
}