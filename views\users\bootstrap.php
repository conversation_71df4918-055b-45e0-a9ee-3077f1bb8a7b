<?php
// Bootstrap file for user view - handles dependency injection
require_once __DIR__ . '/../../db/MYSQLConnection.php';
require_once __DIR__ . '/../../repositories/UserRepository.php';
require_once __DIR__ . '/../../services/UserService.php';
require_once __DIR__ . '/../../validation/UserValidator.php';
require_once __DIR__ . '/../../controllers/UserViewController.php';

// Dependency injection setup
$db = new MYSQLConnection();
$userRepository = new UserRepository($db);
$validator = new UserValidator();
$userService = new UserService($userRepository, $validator);
$viewController = new UserViewController($userService);

// Get data for the view
$viewData = $viewController->index();
