

document.addEventListener('DOMContentLoaded', function() {

    initUserActions();
});


function initUserActions() {
    setupUserForms();
    setupDeleteButtons();
}

function setupUserForms() {
    const userForm = document.getElementById('userForm');
    if (userForm) {
        userForm.addEventListener('submit', function(e) {
            e.preventDefault();

            if (!validateUserForm()) {
                submitForm();
            }
        });
    }
}

function submitForm() {
    const userId = document.getElementById('userId').value;
    const name = document.getElementById('name').value;
    const email = document.getElementById('email').value;

    const data = {
        name: name,
        email: email
    };

   const endpoint = userId ? '/SOLID-Principles/controllers/UserController.php/update' : '/SOLID-Principles/controllers/UserController.php/create';

  const method = userId ? 'PUT' : 'POST';

  fetch(endpoint, {
    method: method,
    headers: {
        'Content-Type': 'application/json'
    },
    body: JSON.stringify(data)
  })
  .then(response => response.json())
  .then(data => {
    if (data.status === 'success') {
      ShowAlert('Success', data.message);
      resetForm();
      fetchUsers();
    } else {
      ShowAlert('Error', data.message);
    }
  })
  .catch(error => {
    ShowAlert('Error', error.message);
  });
}

function showAlert(type, message) {
    const alertEl = document.getElementById('alertMessage');
    alertEl.className = `alert alert-${type}`;
    alertEl.textContent = message;
    alertEl.style.display = 'block';

    setTimeout(() => {
        alertEl.style.display = 'none';
    }, 3000);
}


function validateUserForm() {
    const nameInput = document.getElementById('name');
    const emailInput = document.getElementById('email');
    let isValid = true;

    clearErrors();


    if (!nameInput.value.trim()) {
        displayError(nameInput, 'Name is required');
        isValid = false;
    }

    if (!emailInput.value.trim()) {
        displayError(emailInput, 'Email is required');
        isValid = false;
    } else if (!isValidEmail(emailInput.value)) {
        displayError(emailInput, 'Please enter a valid email');
        isValid = false;
    }

    return isValid;
}


function resetForm() {
    document.getElementById('userId').value = '';
    document.getElementById('formTitle').textContent = 'Create User';
    document.getElementById('userForm').reset();
    clearErrors();
}

function setupDeleteButtons() {
    const deleteButtons = document.querySelectorAll('.delete-user');
    deleteButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            if (!confirm('Are you sure you want to delete this user?')) {
                e.preventDefault();
            }
        });
    });
}


function displayError(inputElement, message) {
    const errorSpan = document.createElement('span');
    errorSpan.className = 'error-message';
    errorSpan.textContent = message;
    errorSpan.style.color = 'red';
    errorSpan.style.fontSize = '12px';
    inputElement.parentNode.appendChild(errorSpan);
    inputElement.classList.add('error');
}


function clearErrors() {
    document.querySelectorAll('.error-message').forEach(el => el.remove());
    document.querySelectorAll('.error').forEach(el => el.classList.remove('error'));
}


function isValidEmail(email) {
    const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return re.test(email);
} 