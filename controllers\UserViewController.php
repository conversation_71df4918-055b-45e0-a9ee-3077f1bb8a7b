<?php
require_once __DIR__ . '/../services/UserServiceInterface.php';

class UserViewController {
    private $userService;
    
    public function __construct(UserServiceInterface $userService) {
        $this->userService = $userService;
    }
    
    public function index() {
        try {
            $users = $this->userService->getAll();
            $successMessage = isset($_GET['success']) ? 
                (isset($_GET['message']) ? $_GET['message'] : 'User created successfully!') : null;
            $errorMessage = isset($_GET['error']) ? $_GET['error'] : null;
            
            return [
                'users' => $users,
                'successMessage' => $successMessage,
                'errorMessage' => $errorMessage
            ];
        } catch (Exception $e) {
            return [
                'users' => [],
                'successMessage' => null,
                'errorMessage' => 'Error loading users: ' . $e->getMessage()
            ];
        }
    }
}
