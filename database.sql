-- Create database if it doesn't exist
CREATE DATABASE IF NOT EXISTS SOLID;
USE SOLID;

-- Create users table
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100) NOT NULL UNIQUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Insert some sample data
INSERT IGNORE INTO users (name, email) VALUES 
('<PERSON>', '<EMAIL>'),
('<PERSON>', '<EMAIL>'),
('<PERSON>', '<EMAIL>');
