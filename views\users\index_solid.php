<?php
// SOLID-compliant view - only handles presentation
require_once __DIR__ . '/bootstrap.php';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Management</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h1 class="mb-4">User Management</h1>

        <?php if ($viewData['successMessage']): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <?php echo htmlspecialchars($viewData['successMessage']); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if ($viewData['errorMessage']): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                Error: <?php echo htmlspecialchars($viewData['errorMessage']); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- User Form -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 id="formTitle">Create User</h5>
            </div>
            <div class="card-body">
                <form id="userForm" action="/SOLID-Principles/api/user.php" method="POST">
                    <input type="hidden" id="userId" name="id" value="">
                    <input type="hidden" name="_html_form" value="1">
                    <input type="hidden" id="formMethod" name="_method" value="POST">
                    
                    <div class="mb-3">
                        <label for="name" class="form-label">Name</label>
                        <input type="text" class="form-control" id="name" name="name" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="email" class="form-label">Email</label>
                        <input type="email" class="form-control" id="email" name="email" required>
                    </div>
                    
                    <button type="submit" class="btn btn-primary">Save</button>
                    <button type="reset" class="btn btn-secondary">Cancel</button>
                </form>
            </div>
        </div>

        <!-- Users Table -->
        <div class="card">
            <div class="card-header">
                <h5>Users</h5>
            </div>
            <div class="card-body">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Name</th>
                            <th>Email</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($viewData['users'] as $user): ?>
                            <tr>
                                <td><?php echo htmlspecialchars($user->id); ?></td>
                                <td><?php echo htmlspecialchars($user->name); ?></td>
                                <td><?php echo htmlspecialchars($user->email); ?></td>
                                <td>
                                    <button onclick='editUser(<?php echo $user->id; ?>, "<?php echo htmlspecialchars($user->name, ENT_QUOTES); ?>", "<?php echo htmlspecialchars($user->email, ENT_QUOTES); ?>")' 
                                            class='btn btn-sm btn-warning me-1'>Edit</button>
                                    <a href='/SOLID-Principles/api/user.php?id=<?php echo $user->id; ?>' 
                                       class='btn btn-sm btn-danger delete-user'>Delete</a>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/user-form.js"></script>
</body>
</html>
