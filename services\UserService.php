<?php
require_once __DIR__ . '/../entities/user.php';
require_once __DIR__ . '/../repositories/UserRepositoryInterface.php';
require_once __DIR__ . '/../validation/UserValidator.php';
require_once __DIR__ . '/../exception/ValidationException.php';
require_once __DIR__ . '/UserServiceInterface.php';

class UserService implements UserServiceInterface {
    private $userRepository;
    private $validator;
    
    public function __construct(UserRepositoryInterface $userRepository, UserValidator $validator) {
        $this->userRepository = $userRepository;
        $this->validator = $validator;
    }
    
    public function getAll() {
        return $this->userRepository->getAll();
    }
    
    public function getById($id) {
        if (!is_numeric($id) || $id <= 0) {
            throw new ValidationException("Invalid user ID");
        }
        
        $user = $this->userRepository->getById($id);
        if (!$user->id) {
            throw new ValidationException("User not found");
        }
        
        return $user;
    }
    
    public function create(User $user) {
        $errors = $this->validator->validate($user);
        if (count($errors) > 0) {
            throw new ValidationException(json_encode($errors));
        }
        
        return $this->userRepository->create($user);
    }
    
    public function update(User $user) {
        $errors = $this->validator->validate($user);
        if (count($errors) > 0) {
            throw new ValidationException(json_encode($errors));
        }
        
        if (!$user->id) {
            throw new ValidationException("User ID is required for update");
        }
        
        return $this->userRepository->update($user);
    }
    
    public function delete($id) {
        if (!is_numeric($id) || $id <= 0) {
            throw new ValidationException("Invalid user ID");
        }
        
        return $this->userRepository->delete($id);
    }
}