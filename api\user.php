<?php
require_once __DIR__ . '/../db/MYSQLConnection.php';
require_once __DIR__ . '/../db/DatabaseConnectionInterface.php';
require_once __DIR__ . '/../repositories/UserRepository.php';
require_once __DIR__ . '/../services/UserService.php';
require_once __DIR__ . '/../validation/UserValidator.php';
require_once __DIR__ . '/../controllers/UserController.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE');
header('Access-Control-Allow-Headers: Content-Type');

$db = new MYSQLConnection();
$userRepository = new UserRepository($db);
$validator = new UserValidator();
$userService = new UserService($userRepository, $validator);
$controller = new UserController($userService);

$method = $_SERVER['REQUEST_METHOD'];

try {
    switch ($method) {
        case 'GET':
            if (isset($_GET['id'])) {
                echo $controller->show($_GET['id']);
            } else {
                echo $controller->index();
            }
            break;
            
        case 'POST':
         
            echo $controller->create();
            break;
            
        case 'PUT':
            $id = isset($_GET['id']) ? $_GET['id'] : null;
            if (!$id) {
                http_response_code(400);
                echo json_encode(['status' => 'error', 'message' => 'ID is required for update']);
                exit;
            }
            echo $controller->update($id);
            break;
            
        case 'DELETE':
            $id = isset($_GET['id']) ? $_GET['id'] : null;
            if (!$id) {
                http_response_code(400);
                echo json_encode(['status' => 'error', 'message' => 'ID is required for delete']);
                exit;
            }
            echo $controller->delete($id);
            break;
            
        default:
            http_response_code(405);
            echo json_encode(['status' => 'error', 'message' => 'Method not allowed']);
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['status' => 'error', 'message' => $e->getMessage()]);
}