<?php
require_once __DIR__ . '/../entities/user.php';

class UserValidator{
    public function validate(User $user){
        $errors = [];

        if (empty($user->name)){
            $errors[] = "Name cannot be empty";
        } elseif (strlen($user->name) < 2){
            $errors['name'] = "Name must be at least 2 characters";
        } elseif (strlen($user->name) > 100){
            $errors['name'] = "Name cannot exceed 100 characters";
        }

        if (empty($user->email)){
            $errors['email'] = "Email cannot be empty";
        } elseif (!filter_var($user->email, FILTER_VALIDATE_EMAIL)){
            $errors['email'] = "Invalid email format";
        } elseif (strlen($user->email) > 100){
            $errors['email'] = "Email cannot exceed 100 characters";
        }

        return $errors;
    }
}